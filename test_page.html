<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Image Generator Test Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #555;
        }
        textarea {
            width: 100%;
            min-height: 100px;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 6px;
            font-size: 16px;
            resize: vertical;
            transition: border-color 0.3s;
        }
        textarea:focus {
            outline: none;
            border-color: #667eea;
        }
        .generate-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            font-size: 16px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            gap: 8px;
            margin: 20px auto;
        }
        .generate-btn:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }
        .generate-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }
        .output {
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 6px;
            border-left: 4px solid #667eea;
        }
        .output h3 {
            margin-top: 0;
            color: #333;
        }
        .prompt-history {
            margin-top: 20px;
        }
        .prompt-item {
            background: white;
            padding: 10px;
            margin: 5px 0;
            border-radius: 4px;
            border-left: 3px solid #667eea;
        }
        .status {
            text-align: center;
            margin: 20px 0;
            font-style: italic;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 AI Image Generator Test Page</h1>
        <p style="text-align: center; color: #666; margin-bottom: 30px;">
            This is a test page to verify the AI Image Automator extension works correctly.
        </p>
        
        <div class="form-group">
            <label for="prompt">Enter your image prompt:</label>
            <textarea 
                id="prompt" 
                name="prompt" 
                placeholder="Type your prompt here to start..." 
                class="w-full bg-transparent outline-none !text-black resize-none transition-all duration-500 text-[16px] focus:border-a focus:shadow-none focus:outline-0 cursor-text scrollable-content h-[50px]"
                style="scrollbar-width: none; height: 48px;"
            ></textarea>
        </div>
        
        <button 
            id="generate-btn" 
            class="generate-btn MuiButtonBase-root MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeLarge MuiButton-containedSizeLarge MuiButton-colorPrimary MuiButton-fullWidth !bg-runware-purple-light !text-white !flex !items-center !justify-center gap-1"
            type="button"
        >
            <span>Generate</span>
            <svg width="16" height="16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12.667 5.334v2h-8.78l1.92-1.92a.664.664 0 1 0-.94-.94l-3.06 3.06c-.26.26-.26.68 0 .94l3.06 3.06a.664.664 0 1 0 .94-.94l-1.92-1.927h9.447c.366 0 .666-.3.666-.667V5.334c0-.367-.3-.667-.666-.667-.367 0-.667.3-.667.667Z" fill="#fff"></path>
            </svg>
        </button>
        
        <div class="status" id="status">Ready to generate images</div>
        
        <div class="output">
            <h3>Generated Images</h3>
            <p>When you click "Generate", this area will show the processing status and results.</p>
            <div id="results"></div>
        </div>
        
        <div class="prompt-history">
            <h3>Prompt History</h3>
            <div id="history">
                <p style="color: #666; font-style: italic;">No prompts generated yet.</p>
            </div>
        </div>
    </div>

    <script>
        let promptCount = 0;
        
        const promptTextarea = document.getElementById('prompt');
        const generateBtn = document.getElementById('generate-btn');
        const statusDiv = document.getElementById('status');
        const resultsDiv = document.getElementById('results');
        const historyDiv = document.getElementById('history');
        
        // Enable/disable button based on textarea content
        promptTextarea.addEventListener('input', function() {
            const hasContent = this.value.trim().length > 0;
            generateBtn.disabled = !hasContent;
            
            if (hasContent) {
                generateBtn.classList.remove('Mui-disabled');
                generateBtn.removeAttribute('disabled');
            } else {
                generateBtn.classList.add('Mui-disabled');
                generateBtn.setAttribute('disabled', 'true');
            }
        });
        
        // Handle generate button click
        generateBtn.addEventListener('click', function() {
            const prompt = promptTextarea.value.trim();
            if (!prompt) return;
            
            promptCount++;
            
            // Disable button and show processing
            generateBtn.disabled = true;
            generateBtn.classList.add('Mui-disabled');
            statusDiv.textContent = 'Generating image...';
            statusDiv.style.color = '#667eea';
            
            // Add to history
            const historyItem = document.createElement('div');
            historyItem.className = 'prompt-item';
            historyItem.innerHTML = `
                <strong>Prompt ${promptCount}:</strong> ${prompt}
                <br><small style="color: #666;">Generated at ${new Date().toLocaleTimeString()}</small>
            `;
            
            if (historyDiv.querySelector('p')) {
                historyDiv.innerHTML = '';
            }
            historyDiv.insertBefore(historyItem, historyDiv.firstChild);
            
            // Simulate image generation process
            setTimeout(() => {
                statusDiv.textContent = 'Image generated successfully!';
                statusDiv.style.color = '#28a745';
                
                resultsDiv.innerHTML = `
                    <div style="background: white; padding: 15px; border-radius: 6px; margin: 10px 0;">
                        <strong>Latest Generation:</strong><br>
                        Prompt: "${prompt}"<br>
                        Status: ✅ Completed<br>
                        <small style="color: #666;">Generated at ${new Date().toLocaleString()}</small>
                    </div>
                ` + resultsDiv.innerHTML;
                
                // Re-enable button
                setTimeout(() => {
                    generateBtn.disabled = false;
                    generateBtn.classList.remove('Mui-disabled');
                    statusDiv.textContent = 'Ready to generate images';
                    statusDiv.style.color = '#666';
                }, 1000);
                
            }, 2000); // Simulate 2 second generation time
        });
        
        // Initialize button state
        generateBtn.disabled = true;
        generateBtn.classList.add('Mui-disabled');
        generateBtn.setAttribute('disabled', 'true');
        
        // Add some helpful text
        const helpText = document.createElement('div');
        helpText.style.cssText = 'background: #e3f2fd; padding: 15px; border-radius: 6px; margin: 20px 0; border-left: 4px solid #2196f3;';
        helpText.innerHTML = `
            <strong>🧪 Testing the Extension:</strong><br>
            1. Install the AI Image Automator extension<br>
            2. Load a text file with prompts using the extension popup<br>
            3. Click "Start Automation" in the extension<br>
            4. Watch as the extension automatically fills prompts and clicks generate!
        `;
        document.querySelector('.container').appendChild(helpText);
    </script>
</body>
</html>
