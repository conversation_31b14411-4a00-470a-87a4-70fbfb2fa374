<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Image Automator</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>AI Image Automator</h1>
            <button id="openWindow" class="window-btn" title="Open in separate window">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3m0 18h3a2 2 0 0 0 2-2v-3M3 16v3a2 2 0 0 0 2 2h3"/>
                </svg>
            </button>
        </div>
        
        <div class="section">
            <label for="fileInput" class="file-label">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                    <polyline points="14,2 14,8 20,8"/>
                    <line x1="16" y1="13" x2="8" y2="13"/>
                    <line x1="16" y1="17" x2="8" y2="17"/>
                    <polyline points="10,9 9,9 8,9"/>
                </svg>
                Select Text File with Prompts
            </label>
            <input type="file" id="fileInput" accept=".txt" style="display: none;">
            <div id="fileName" class="file-name"></div>
        </div>
        
        <div class="section">
            <label for="waitTime">Wait Time Between Prompts (seconds):</label>
            <input type="number" id="waitTime" min="1" max="300" value="30" class="input-field">
        </div>
        
        <div class="section">
            <div class="prompt-info">
                <span id="promptCount">No prompts loaded</span>
                <span id="currentPrompt"></span>
            </div>
        </div>
        
        <div class="section">
            <button id="testConnection" class="btn" style="background: #28a745; color: white; margin-bottom: 5px;">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M9 12l2 2 4-4"/>
                    <circle cx="12" cy="12" r="10"/>
                </svg>
                Test Connection
            </button>
            <button id="debugElements" class="btn" style="background: #17a2b8; color: white; margin-bottom: 10px;">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <circle cx="12" cy="12" r="3"/>
                    <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1"/>
                </svg>
                Debug Elements
            </button>
            <button id="startAutomation" class="btn btn-primary" disabled>
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <polygon points="5,3 19,12 5,21"/>
                </svg>
                Start Automation
            </button>
            <button id="stopAutomation" class="btn btn-secondary" disabled>
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <rect x="6" y="6" width="12" height="12"/>
                </svg>
                Stop
            </button>
        </div>
        
        <div class="section">
            <div class="status" id="status">Ready to load prompts</div>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </div>
        
        <div class="section">
            <details class="help-section">
                <summary>How to use</summary>
                <ol>
                    <li>Select a text file containing prompts (one per line)</li>
                    <li>Set the wait time between each prompt</li>
                    <li>Navigate to your AI image generation website</li>
                    <li>Click "Start Automation"</li>
                    <li>The extension will automatically paste prompts and click generate</li>
                </ol>
            </details>
        </div>
    </div>
    
    <script src="popup.js"></script>
</body>
</html>
