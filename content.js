// AI Image Automator Content Script
console.log('🤖 AI Image Automator: Content script loaded on', window.location.href);
console.log('🤖 AI Image Automator: Script injection time:', new Date().toISOString());

class AIImageAutomationEngine {
    constructor() {
        console.log('🤖 AI Image Automator: Initializing automation engine...');
        this.isRunning = false;
        this.prompts = [];
        this.currentIndex = 0;
        this.waitTime = 30000; // Default 30 seconds
        this.timeoutId = null;
        
        // Selectors for the AI image generation interface
        this.textareaSelector = 'textarea[placeholder*="prompt"], textarea[class*="prompt"], textarea[name*="prompt"]';
        this.generateButtonSelector = 'button[id*="submit"], button[class*="generate"], button[type="submit"]';
        
        // Alternative selectors based on the provided HTML structure
        this.specificTextareaSelector = 'textarea.w-full.bg-transparent.outline-none';
        this.specificButtonSelector = 'button.MuiButton-root.MuiButton-contained.MuiButton-containedPrimary';
        
        this.bindMessageListener();
        console.log('🤖 AI Image Automator: Engine initialized successfully');
    }
    
    bindMessageListener() {
        // Firefox compatibility
        const runtimeAPI = typeof browser !== 'undefined' ? browser.runtime : chrome.runtime;
        runtimeAPI.onMessage.addListener((message, sender, sendResponse) => {
            this.handleMessage(message, sender, sendResponse);
            return true; // Keep the message channel open for async responses
        });
    }
    
    handleMessage(message, sender, sendResponse) {
        switch (message.action) {
            case 'ping':
                sendResponse({ success: true, message: 'Content script is active' });
                break;

            case 'debugElements':
                this.debugElements(sendResponse);
                break;

            case 'startAutomation':
                this.startAutomation(message.prompts, message.currentIndex, message.waitTime);
                sendResponse({ success: true });
                break;

            case 'stopAutomation':
                this.stopAutomation();
                sendResponse({ success: true });
                break;

            default:
                sendResponse({ success: false, error: 'Unknown action' });
        }
    }

    debugElements(sendResponse) {
        console.log('=== AI Automator Debug Elements ===');

        const allTextareas = document.querySelectorAll('textarea');
        const allButtons = document.querySelectorAll('button');

        console.log(`Found ${allTextareas.length} textareas:`);
        allTextareas.forEach((ta, i) => {
            console.log(`  Textarea ${i}:`, {
                placeholder: ta.placeholder,
                className: ta.className,
                name: ta.name,
                id: ta.id,
                value: ta.value,
                visible: ta.offsetHeight > 0 && window.getComputedStyle(ta).display !== 'none',
                focused: document.activeElement === ta
            });
        });

        console.log(`Found ${allButtons.length} buttons:`);
        allButtons.forEach((btn, i) => {
            console.log(`  Button ${i}:`, {
                text: btn.textContent?.trim(),
                className: btn.className,
                id: btn.id,
                type: btn.type,
                disabled: btn.disabled,
                visible: btn.offsetHeight > 0 && window.getComputedStyle(btn).display !== 'none'
            });
        });

        // Test element finding
        const foundTextarea = this.findTextarea();
        const foundButton = this.findGenerateButton();

        console.log('Selected elements:');
        console.log('  Textarea:', foundTextarea);
        console.log('  Button:', foundButton);

        sendResponse({
            success: true,
            textareas: allTextareas.length,
            buttons: allButtons.length,
            foundTextarea: !!foundTextarea,
            foundButton: !!foundButton
        });
    }
    
    findTextarea() {
        // Try specific selector first
        let textarea = document.querySelector(this.specificTextareaSelector);

        // If not found, try generic selectors
        if (!textarea) {
            textarea = document.querySelector(this.textareaSelector);
        }

        // If still not found, try to find any textarea that might be for prompts
        if (!textarea) {
            const textareas = document.querySelectorAll('textarea');
            for (const ta of textareas) {
                const placeholder = ta.placeholder?.toLowerCase() || '';
                const className = ta.className?.toLowerCase() || '';
                const name = ta.name?.toLowerCase() || '';

                if (placeholder.includes('prompt') ||
                    placeholder.includes('describe') ||
                    placeholder.includes('type your prompt') ||
                    placeholder.includes('enter prompt') ||
                    className.includes('prompt') ||
                    name.includes('prompt')) {
                    textarea = ta;
                    break;
                }
            }
        }

        // Last resort: find the first visible textarea
        if (!textarea) {
            const textareas = document.querySelectorAll('textarea');
            for (const ta of textareas) {
                const style = window.getComputedStyle(ta);
                if (style.display !== 'none' && style.visibility !== 'hidden' && ta.offsetHeight > 0) {
                    textarea = ta;
                    break;
                }
            }
        }

        return textarea;
    }
    
    findGenerateButton() {
        // Try specific selector first
        let button = document.querySelector(this.specificButtonSelector);

        // If not found, try generic selectors
        if (!button) {
            button = document.querySelector(this.generateButtonSelector);
        }

        // If still not found, look for buttons with generate-related text
        if (!button) {
            const buttons = document.querySelectorAll('button');
            for (const btn of buttons) {
                const text = btn.textContent?.toLowerCase().trim() || '';
                const className = btn.className?.toLowerCase() || '';
                const id = btn.id?.toLowerCase() || '';

                if (text === 'generate' ||
                    text.includes('generate') ||
                    text.includes('create') ||
                    text.includes('submit') ||
                    text.includes('run') ||
                    className.includes('generate') ||
                    className.includes('submit') ||
                    id.includes('generate') ||
                    id.includes('submit')) {
                    // Make sure the button is visible and not disabled permanently
                    const style = window.getComputedStyle(btn);
                    if (style.display !== 'none' && style.visibility !== 'hidden') {
                        button = btn;
                        break;
                    }
                }
            }
        }

        return button;
    }
    
    async startAutomation(prompts, startIndex, waitTime) {
        console.log('AI Automator: startAutomation called with:', { prompts: prompts?.length, startIndex, waitTime });

        if (this.isRunning) {
            console.log('AI Automator: Already running, aborting');
            this.sendMessage('automationError', { error: 'Automation is already running' });
            return;
        }

        this.prompts = prompts;
        this.currentIndex = startIndex || 0;
        this.waitTime = waitTime || 30000;
        this.isRunning = true;

        console.log('AI Automator: Starting automation with', this.prompts.length, 'prompts');
        this.sendMessage('updateStatus', { status: 'Checking page elements...' });

        // Debug: Log all textareas and buttons found
        console.log('AI Automator Debug: Looking for elements...');
        const allTextareas = document.querySelectorAll('textarea');
        const allButtons = document.querySelectorAll('button');
        console.log('Found textareas:', allTextareas.length);
        console.log('Found buttons:', allButtons.length);

        allTextareas.forEach((ta, i) => {
            console.log(`Textarea ${i}:`, {
                placeholder: ta.placeholder,
                className: ta.className,
                name: ta.name,
                id: ta.id,
                visible: ta.offsetHeight > 0 && window.getComputedStyle(ta).display !== 'none'
            });
        });

        allButtons.forEach((btn, i) => {
            console.log(`Button ${i}:`, {
                text: btn.textContent?.trim(),
                className: btn.className,
                id: btn.id,
                type: btn.type,
                disabled: btn.disabled,
                visible: btn.offsetHeight > 0 && window.getComputedStyle(btn).display !== 'none'
            });
        });

        // Verify that we can find the necessary elements
        const textarea = this.findTextarea();
        const button = this.findGenerateButton();

        console.log('Selected textarea:', textarea);
        console.log('Selected button:', button);

        if (!textarea) {
            // Last resort: try to find ANY textarea
            const allTextareas = document.querySelectorAll('textarea');
            if (allTextareas.length > 0) {
                textarea = allTextareas[0]; // Use the first textarea found
                console.log('Using fallback textarea:', textarea);
            } else {
                this.sendMessage('automationError', { error: 'Could not find any textarea on this page. Check console for debug info.' });
                this.isRunning = false;
                return;
            }
        }

        if (!button) {
            // Last resort: try to find ANY button that might be a submit button
            const allButtons = document.querySelectorAll('button');
            for (const btn of allButtons) {
                if (!btn.disabled && btn.offsetHeight > 0) {
                    button = btn;
                    console.log('Using fallback button:', button);
                    break;
                }
            }

            if (!button) {
                this.sendMessage('automationError', { error: 'Could not find any clickable button on this page. Check console for debug info.' });
                this.isRunning = false;
                return;
            }
        }

        this.sendMessage('updateStatus', { status: 'Starting automation...' });

        // Start processing prompts
        console.log('AI Automator: About to start processing prompts...');
        this.processNextPrompt();
    }
    
    stopAutomation() {
        this.isRunning = false;
        if (this.timeoutId) {
            clearTimeout(this.timeoutId);
            this.timeoutId = null;
        }
        this.sendMessage('updateStatus', { status: 'Automation stopped' });
    }
    
    async processNextPrompt() {
        console.log('AI Automator: processNextPrompt called, currentIndex:', this.currentIndex, 'total prompts:', this.prompts?.length);

        if (!this.isRunning || this.currentIndex >= this.prompts.length) {
            console.log('AI Automator: Stopping - isRunning:', this.isRunning, 'currentIndex:', this.currentIndex);
            if (this.currentIndex >= this.prompts.length) {
                this.sendMessage('automationComplete', {});
            }
            this.isRunning = false;
            return;
        }
        
        const currentPrompt = this.prompts[this.currentIndex];
        
        this.sendMessage('updateStatus', { 
            status: `Processing prompt ${this.currentIndex + 1}/${this.prompts.length}` 
        });
        
        this.sendMessage('updateProgress', { 
            currentIndex: this.currentIndex 
        });
        
        try {
            console.log('AI Automator: Finding elements for prompt', this.currentIndex + 1);

            // Find elements again (in case page has changed)
            const textarea = this.findTextarea();
            const button = this.findGenerateButton();

            console.log('AI Automator: Found elements - textarea:', !!textarea, 'button:', !!button);

            if (!textarea || !button) {
                console.error('AI Automator: Elements not found - textarea:', textarea, 'button:', button);
                this.sendMessage('automationError', {
                    error: 'Page elements not found. Please refresh and try again.'
                });
                this.isRunning = false;
                return;
            }
            
            // Clear existing text and paste new prompt
            console.log('Pasting prompt:', currentPrompt);
            textarea.focus();

            // Multiple methods to ensure the text is set properly
            textarea.value = '';
            textarea.value = currentPrompt;

            // Trigger various events that might be needed
            textarea.dispatchEvent(new Event('input', { bubbles: true, cancelable: true }));
            textarea.dispatchEvent(new Event('change', { bubbles: true, cancelable: true }));
            textarea.dispatchEvent(new Event('keyup', { bubbles: true, cancelable: true }));
            textarea.dispatchEvent(new Event('paste', { bubbles: true, cancelable: true }));

            // For React/modern frameworks
            const nativeInputValueSetter = Object.getOwnPropertyDescriptor(window.HTMLTextAreaElement.prototype, "value").set;
            nativeInputValueSetter.call(textarea, currentPrompt);
            textarea.dispatchEvent(new Event('input', { bubbles: true }));

            console.log('Textarea value after setting:', textarea.value);
            
            // Wait a moment for the UI to update
            console.log('AI Automator: Waiting for UI to update...');
            await this.sleep(1000);

            // Check if button is enabled
            console.log('AI Automator: Button disabled status:', button.disabled);
            if (button.disabled) {
                this.sendMessage('updateStatus', {
                    status: 'Waiting for generate button to be enabled...'
                });

                // Wait up to 10 seconds for button to be enabled
                let attempts = 0;
                while (button.disabled && attempts < 20 && this.isRunning) {
                    console.log('AI Automator: Waiting for button to be enabled, attempt:', attempts + 1);
                    await this.sleep(500);
                    attempts++;
                }

                if (button.disabled) {
                    console.error('AI Automator: Button remained disabled after 10 seconds');
                    this.sendMessage('automationError', {
                        error: 'Generate button remained disabled. Check if prompt is valid.'
                    });
                    this.isRunning = false;
                    return;
                }
            }
            
            // Click the generate button
            console.log('AI Automator: Clicking generate button...');
            button.click();

            this.sendMessage('updateStatus', {
                status: `Prompt submitted. Waiting ${this.waitTime / 1000} seconds...`
            });

            // Move to next prompt
            this.currentIndex++;
            console.log('AI Automator: Moving to next prompt, currentIndex now:', this.currentIndex);

            // Wait before processing next prompt
            console.log('AI Automator: Setting timeout for', this.waitTime, 'ms');
            this.timeoutId = setTimeout(() => {
                console.log('AI Automator: Timeout completed, processing next prompt...');
                this.processNextPrompt();
            }, this.waitTime);
            
        } catch (error) {
            this.sendMessage('automationError', { 
                error: `Error processing prompt: ${error.message}` 
            });
            this.isRunning = false;
        }
    }
    
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    sendMessage(action, data = {}) {
        // Firefox compatibility
        const runtimeAPI = typeof browser !== 'undefined' ? browser.runtime : chrome.runtime;
        runtimeAPI.sendMessage({
            action: action,
            ...data
        });
    }
}

// Initialize the automation engine
console.log('AI Image Automator: Content script loaded');
const automationEngine = new AIImageAutomationEngine();

// Add visual indicator when extension is active
const indicator = document.createElement('div');
indicator.id = 'ai-automator-indicator';
indicator.innerHTML = '🤖 AI Automator Ready';
indicator.style.cssText = `
    position: fixed;
    top: 10px;
    right: 10px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 8px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: bold;
    z-index: 10000;
    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
    transition: all 0.3s ease;
    cursor: pointer;
`;

document.body.appendChild(indicator);

// Hide indicator after 3 seconds
setTimeout(() => {
    if (indicator.parentNode) {
        indicator.style.opacity = '0';
        setTimeout(() => {
            if (indicator.parentNode) {
                indicator.parentNode.removeChild(indicator);
            }
        }, 300);
    }
}, 3000);
