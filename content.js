class AIImageAutomationEngine {
    constructor() {
        this.isRunning = false;
        this.prompts = [];
        this.currentIndex = 0;
        this.waitTime = 30000; // Default 30 seconds
        this.timeoutId = null;
        
        // Selectors for the AI image generation interface
        this.textareaSelector = 'textarea[placeholder*="prompt"], textarea[class*="prompt"], textarea[name*="prompt"]';
        this.generateButtonSelector = 'button[id*="submit"], button[class*="generate"], button[type="submit"]';
        
        // Alternative selectors based on the provided HTML structure
        this.specificTextareaSelector = 'textarea.w-full.bg-transparent.outline-none';
        this.specificButtonSelector = 'button.MuiButton-root.MuiButton-contained.MuiButton-containedPrimary';
        
        this.bindMessageListener();
    }
    
    bindMessageListener() {
        // Firefox compatibility
        const runtimeAPI = typeof browser !== 'undefined' ? browser.runtime : chrome.runtime;
        runtimeAPI.onMessage.addListener((message, sender, sendResponse) => {
            this.handleMessage(message, sender, sendResponse);
            return true; // Keep the message channel open for async responses
        });
    }
    
    handleMessage(message, sender, sendResponse) {
        switch (message.action) {
            case 'ping':
                sendResponse({ success: true, message: 'Content script is active' });
                break;

            case 'startAutomation':
                this.startAutomation(message.prompts, message.currentIndex, message.waitTime);
                sendResponse({ success: true });
                break;

            case 'stopAutomation':
                this.stopAutomation();
                sendResponse({ success: true });
                break;

            default:
                sendResponse({ success: false, error: 'Unknown action' });
        }
    }
    
    findTextarea() {
        // Try specific selector first
        let textarea = document.querySelector(this.specificTextareaSelector);

        // If not found, try generic selectors
        if (!textarea) {
            textarea = document.querySelector(this.textareaSelector);
        }

        // If still not found, try to find any textarea that might be for prompts
        if (!textarea) {
            const textareas = document.querySelectorAll('textarea');
            for (const ta of textareas) {
                const placeholder = ta.placeholder?.toLowerCase() || '';
                const className = ta.className?.toLowerCase() || '';
                const name = ta.name?.toLowerCase() || '';

                if (placeholder.includes('prompt') ||
                    placeholder.includes('describe') ||
                    placeholder.includes('type your prompt') ||
                    placeholder.includes('enter prompt') ||
                    className.includes('prompt') ||
                    name.includes('prompt')) {
                    textarea = ta;
                    break;
                }
            }
        }

        // Last resort: find the first visible textarea
        if (!textarea) {
            const textareas = document.querySelectorAll('textarea');
            for (const ta of textareas) {
                const style = window.getComputedStyle(ta);
                if (style.display !== 'none' && style.visibility !== 'hidden' && ta.offsetHeight > 0) {
                    textarea = ta;
                    break;
                }
            }
        }

        return textarea;
    }
    
    findGenerateButton() {
        // Try specific selector first
        let button = document.querySelector(this.specificButtonSelector);

        // If not found, try generic selectors
        if (!button) {
            button = document.querySelector(this.generateButtonSelector);
        }

        // If still not found, look for buttons with generate-related text
        if (!button) {
            const buttons = document.querySelectorAll('button');
            for (const btn of buttons) {
                const text = btn.textContent?.toLowerCase().trim() || '';
                const className = btn.className?.toLowerCase() || '';
                const id = btn.id?.toLowerCase() || '';

                if (text === 'generate' ||
                    text.includes('generate') ||
                    text.includes('create') ||
                    text.includes('submit') ||
                    text.includes('run') ||
                    className.includes('generate') ||
                    className.includes('submit') ||
                    id.includes('generate') ||
                    id.includes('submit')) {
                    // Make sure the button is visible and not disabled permanently
                    const style = window.getComputedStyle(btn);
                    if (style.display !== 'none' && style.visibility !== 'hidden') {
                        button = btn;
                        break;
                    }
                }
            }
        }

        return button;
    }
    
    async startAutomation(prompts, startIndex, waitTime) {
        if (this.isRunning) {
            this.sendMessage('automationError', { error: 'Automation is already running' });
            return;
        }

        this.prompts = prompts;
        this.currentIndex = startIndex || 0;
        this.waitTime = waitTime || 30000;
        this.isRunning = true;

        this.sendMessage('updateStatus', { status: 'Checking page elements...' });

        // Debug: Log all textareas and buttons found
        console.log('AI Automator Debug: Looking for elements...');
        const allTextareas = document.querySelectorAll('textarea');
        const allButtons = document.querySelectorAll('button');
        console.log('Found textareas:', allTextareas.length);
        console.log('Found buttons:', allButtons.length);

        allTextareas.forEach((ta, i) => {
            console.log(`Textarea ${i}:`, {
                placeholder: ta.placeholder,
                className: ta.className,
                name: ta.name,
                id: ta.id
            });
        });

        allButtons.forEach((btn, i) => {
            console.log(`Button ${i}:`, {
                text: btn.textContent?.trim(),
                className: btn.className,
                id: btn.id,
                type: btn.type
            });
        });

        // Verify that we can find the necessary elements
        const textarea = this.findTextarea();
        const button = this.findGenerateButton();

        console.log('Selected textarea:', textarea);
        console.log('Selected button:', button);

        if (!textarea) {
            this.sendMessage('automationError', { error: 'Could not find prompt textarea on this page. Check console for debug info.' });
            this.isRunning = false;
            return;
        }

        if (!button) {
            this.sendMessage('automationError', { error: 'Could not find generate button on this page. Check console for debug info.' });
            this.isRunning = false;
            return;
        }

        this.sendMessage('updateStatus', { status: 'Starting automation...' });

        // Start processing prompts
        this.processNextPrompt();
    }
    
    stopAutomation() {
        this.isRunning = false;
        if (this.timeoutId) {
            clearTimeout(this.timeoutId);
            this.timeoutId = null;
        }
        this.sendMessage('updateStatus', { status: 'Automation stopped' });
    }
    
    async processNextPrompt() {
        if (!this.isRunning || this.currentIndex >= this.prompts.length) {
            if (this.currentIndex >= this.prompts.length) {
                this.sendMessage('automationComplete', {});
            }
            this.isRunning = false;
            return;
        }
        
        const currentPrompt = this.prompts[this.currentIndex];
        
        this.sendMessage('updateStatus', { 
            status: `Processing prompt ${this.currentIndex + 1}/${this.prompts.length}` 
        });
        
        this.sendMessage('updateProgress', { 
            currentIndex: this.currentIndex 
        });
        
        try {
            // Find elements again (in case page has changed)
            const textarea = this.findTextarea();
            const button = this.findGenerateButton();
            
            if (!textarea || !button) {
                this.sendMessage('automationError', { 
                    error: 'Page elements not found. Please refresh and try again.' 
                });
                this.isRunning = false;
                return;
            }
            
            // Clear existing text and paste new prompt
            textarea.focus();
            textarea.select();
            textarea.value = '';
            
            // Use different methods to set the value to ensure it works
            textarea.value = currentPrompt;
            textarea.dispatchEvent(new Event('input', { bubbles: true }));
            textarea.dispatchEvent(new Event('change', { bubbles: true }));
            
            // Wait a moment for the UI to update
            await this.sleep(1000);
            
            // Check if button is enabled
            if (button.disabled) {
                this.sendMessage('updateStatus', { 
                    status: 'Waiting for generate button to be enabled...' 
                });
                
                // Wait up to 10 seconds for button to be enabled
                let attempts = 0;
                while (button.disabled && attempts < 20) {
                    await this.sleep(500);
                    attempts++;
                }
                
                if (button.disabled) {
                    this.sendMessage('automationError', { 
                        error: 'Generate button remained disabled' 
                    });
                    this.isRunning = false;
                    return;
                }
            }
            
            // Click the generate button
            button.click();
            
            this.sendMessage('updateStatus', { 
                status: `Prompt submitted. Waiting ${this.waitTime / 1000} seconds...` 
            });
            
            // Move to next prompt
            this.currentIndex++;
            
            // Wait before processing next prompt
            this.timeoutId = setTimeout(() => {
                this.processNextPrompt();
            }, this.waitTime);
            
        } catch (error) {
            this.sendMessage('automationError', { 
                error: `Error processing prompt: ${error.message}` 
            });
            this.isRunning = false;
        }
    }
    
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    sendMessage(action, data = {}) {
        // Firefox compatibility
        const runtimeAPI = typeof browser !== 'undefined' ? browser.runtime : chrome.runtime;
        runtimeAPI.sendMessage({
            action: action,
            ...data
        });
    }
}

// Initialize the automation engine
console.log('AI Image Automator: Content script loaded');
const automationEngine = new AIImageAutomationEngine();

// Add visual indicator when extension is active
const indicator = document.createElement('div');
indicator.id = 'ai-automator-indicator';
indicator.innerHTML = '🤖 AI Automator Ready';
indicator.style.cssText = `
    position: fixed;
    top: 10px;
    right: 10px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 8px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: bold;
    z-index: 10000;
    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
    transition: all 0.3s ease;
    cursor: pointer;
`;

document.body.appendChild(indicator);

// Hide indicator after 3 seconds
setTimeout(() => {
    if (indicator.parentNode) {
        indicator.style.opacity = '0';
        setTimeout(() => {
            if (indicator.parentNode) {
                indicator.parentNode.removeChild(indicator);
            }
        }, 300);
    }
}, 3000);
