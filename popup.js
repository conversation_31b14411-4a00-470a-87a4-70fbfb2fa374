class AIImageAutomator {
    constructor() {
        this.prompts = [];
        this.currentPromptIndex = 0;
        this.isRunning = false;
        this.waitTime = 30;
        
        this.initializeElements();
        this.bindEvents();
        this.loadStoredData();
    }
    
    initializeElements() {
        this.fileInput = document.getElementById('fileInput');
        this.fileName = document.getElementById('fileName');
        this.waitTimeInput = document.getElementById('waitTime');
        this.promptCount = document.getElementById('promptCount');
        this.currentPrompt = document.getElementById('currentPrompt');
        this.startBtn = document.getElementById('startAutomation');
        this.stopBtn = document.getElementById('stopAutomation');
        this.testBtn = document.getElementById('testConnection');
        this.debugBtn = document.getElementById('debugElements');
        this.status = document.getElementById('status');
        this.progressFill = document.getElementById('progressFill');
        this.openWindowBtn = document.getElementById('openWindow');
    }
    
    bindEvents() {
        this.fileInput.addEventListener('change', (e) => this.handleFileSelect(e));
        this.waitTimeInput.addEventListener('change', (e) => this.updateWaitTime(e));
        this.startBtn.addEventListener('click', () => this.startAutomation());
        this.stopBtn.addEventListener('click', () => this.stopAutomation());
        this.testBtn.addEventListener('click', () => this.testConnection());
        this.debugBtn.addEventListener('click', () => this.debugElements());
        this.openWindowBtn.addEventListener('click', () => this.openInWindow());
        
        // Listen for messages from content script - Firefox compatibility
        const runtimeAPI = typeof browser !== 'undefined' ? browser.runtime : chrome.runtime;
        runtimeAPI.onMessage.addListener((message, sender, sendResponse) => {
            this.handleMessage(message, sender, sendResponse);
        });
    }
    
    async loadStoredData() {
        try {
            // Firefox compatibility: use browser.storage if chrome.storage fails
            const storageAPI = typeof browser !== 'undefined' ? browser.storage : chrome.storage;
            const result = await storageAPI.local.get(['prompts', 'waitTime', 'currentIndex']);

            if (result && result.prompts) {
                this.prompts = result.prompts;
                this.updatePromptDisplay();
            }
            if (result && result.waitTime) {
                this.waitTime = result.waitTime;
                this.waitTimeInput.value = this.waitTime;
            }
            if (result && result.currentIndex !== undefined) {
                this.currentPromptIndex = result.currentIndex;
                this.updateProgress();
            }
        } catch (error) {
            console.error('Error loading stored data:', error);
        }
    }
    
    async saveData() {
        try {
            // Firefox compatibility: use browser.storage if chrome.storage fails
            const storageAPI = typeof browser !== 'undefined' ? browser.storage : chrome.storage;
            await storageAPI.local.set({
                prompts: this.prompts,
                waitTime: this.waitTime,
                currentIndex: this.currentPromptIndex
            });
        } catch (error) {
            console.error('Error saving data:', error);
        }
    }
    
    handleFileSelect(event) {
        const file = event.target.files[0];
        if (!file) return;
        
        this.fileName.textContent = file.name;
        
        const reader = new FileReader();
        reader.onload = (e) => {
            const content = e.target.result;
            this.parsePrompts(content);
        };
        reader.readAsText(file);
    }
    
    parsePrompts(content) {
        // Split by lines and filter out empty lines
        this.prompts = content
            .split('\n')
            .map(line => line.trim())
            .filter(line => line.length > 0);
        
        this.currentPromptIndex = 0;
        this.updatePromptDisplay();
        this.saveData();
        
        this.status.textContent = `Loaded ${this.prompts.length} prompts`;
        this.startBtn.disabled = this.prompts.length === 0;
    }
    
    updatePromptDisplay() {
        if (this.prompts.length === 0) {
            this.promptCount.textContent = 'No prompts loaded';
            this.currentPrompt.textContent = '';
        } else {
            this.promptCount.textContent = `${this.prompts.length} prompts loaded`;
            this.currentPrompt.textContent = `Current: ${this.currentPromptIndex + 1}/${this.prompts.length}`;
        }
        this.updateProgress();
    }
    
    updateProgress() {
        if (this.prompts.length === 0) {
            this.progressFill.style.width = '0%';
        } else {
            const progress = (this.currentPromptIndex / this.prompts.length) * 100;
            this.progressFill.style.width = `${progress}%`;
        }
    }
    
    updateWaitTime(event) {
        this.waitTime = parseInt(event.target.value);
        this.saveData();
    }

    async testConnection() {
        this.status.textContent = 'Testing connection to page...';
        this.status.classList.add('updating');

        try {
            const tabsAPI = typeof browser !== 'undefined' ? browser.tabs : chrome.tabs;
            const tabs = await tabsAPI.query({ active: true, currentWindow: true });

            if (!tabs || tabs.length === 0) {
                this.status.textContent = 'No active tab found';
                this.status.classList.remove('updating');
                return;
            }

            try {
                // Send a ping message to content script
                const response = await tabsAPI.sendMessage(tabs[0].id, { action: 'ping' });

                if (response && response.success) {
                    this.status.textContent = '✅ Connection successful! Content script is active.';
                } else {
                    this.status.textContent = '❌ Content script not responding';
                }
            } catch (pingError) {
                console.log('Content script not responding, trying to inject...');

                // Try to inject the content script manually
                try {
                    // Use background script to inject content script
                    const runtimeAPI = typeof browser !== 'undefined' ? browser.runtime : chrome.runtime;
                    const injectResponse = await runtimeAPI.sendMessage({
                        action: 'injectContentScript',
                        tabId: tabs[0].id
                    });

                    if (injectResponse && injectResponse.success) {
                        // Wait a moment for injection
                        await new Promise(resolve => setTimeout(resolve, 1000));

                        // Try ping again
                        const response = await tabsAPI.sendMessage(tabs[0].id, { action: 'ping' });

                        if (response && response.success) {
                            this.status.textContent = '✅ Connection successful! (Content script injected)';
                        } else {
                            this.status.textContent = '❌ Content script injection failed';
                        }
                    } else {
                        this.status.textContent = '❌ Cannot inject content script. Try refreshing the page.';
                    }
                } catch (injectError) {
                    console.error('Content script injection failed:', injectError);
                    this.status.textContent = '❌ Cannot inject content script. Try refreshing the page.';
                }
            }
        } catch (error) {
            console.error('Test connection error:', error);
            this.status.textContent = `❌ Connection failed: ${error.message}`;
        }

        this.status.classList.remove('updating');

        // Reset status after 5 seconds
        setTimeout(() => {
            this.status.textContent = 'Ready to load prompts';
        }, 5000);
    }

    async debugElements() {
        this.status.textContent = 'Debugging page elements...';
        this.status.classList.add('updating');

        try {
            const tabsAPI = typeof browser !== 'undefined' ? browser.tabs : chrome.tabs;
            const tabs = await tabsAPI.query({ active: true, currentWindow: true });

            if (!tabs || tabs.length === 0) {
                this.status.textContent = 'No active tab found';
                this.status.classList.remove('updating');
                return;
            }

            // Inject content script if needed
            try {
                await tabsAPI.sendMessage(tabs[0].id, { action: 'ping' });
            } catch (pingError) {
                const runtimeAPI = typeof browser !== 'undefined' ? browser.runtime : chrome.runtime;
                await runtimeAPI.sendMessage({
                    action: 'injectContentScript',
                    tabId: tabs[0].id
                });
                await new Promise(resolve => setTimeout(resolve, 1000));
            }

            // Send debug message
            const response = await tabsAPI.sendMessage(tabs[0].id, { action: 'debugElements' });

            if (response && response.success) {
                this.status.textContent = `Found: ${response.textareas} textareas, ${response.buttons} buttons. Check console for details.`;
            } else {
                this.status.textContent = 'Debug failed - check console';
            }
        } catch (error) {
            console.error('Debug error:', error);
            this.status.textContent = `Debug error: ${error.message}`;
        }

        this.status.classList.remove('updating');

        // Reset status after 5 seconds
        setTimeout(() => {
            this.status.textContent = 'Ready to load prompts';
        }, 5000);
    }
    
    async startAutomation() {
        if (this.prompts.length === 0) {
            this.status.textContent = 'Please load prompts first';
            return;
        }

        try {
            // Firefox compatibility: use browser.tabs if chrome.tabs fails
            const tabsAPI = typeof browser !== 'undefined' ? browser.tabs : chrome.tabs;
            const tabs = await tabsAPI.query({ active: true, currentWindow: true });

            if (!tabs || tabs.length === 0) {
                this.status.textContent = 'No active tab found';
                return;
            }

            this.isRunning = true;
            this.startBtn.disabled = true;
            this.stopBtn.disabled = false;
            this.status.textContent = 'Checking content script...';
            this.status.classList.add('updating');

            // First, check if content script is responding
            try {
                await tabsAPI.sendMessage(tabs[0].id, { action: 'ping' });
            } catch (pingError) {
                console.log('Content script not responding, injecting...');
                this.status.textContent = 'Injecting content script...';

                try {
                    // Use background script to inject content script
                    const runtimeAPI = typeof browser !== 'undefined' ? browser.runtime : chrome.runtime;
                    const injectResponse = await runtimeAPI.sendMessage({
                        action: 'injectContentScript',
                        tabId: tabs[0].id
                    });

                    if (!injectResponse || !injectResponse.success) {
                        throw new Error('Background script injection failed');
                    }

                    await new Promise(resolve => setTimeout(resolve, 1000));
                } catch (injectError) {
                    console.error('Failed to inject content script:', injectError);
                    this.status.textContent = 'Failed to inject content script. Please refresh the page.';
                    this.isRunning = false;
                    this.startBtn.disabled = false;
                    this.stopBtn.disabled = true;
                    this.status.classList.remove('updating');
                    return;
                }
            }

            this.status.textContent = 'Starting automation...';

            // Send message to content script to start automation
            await tabsAPI.sendMessage(tabs[0].id, {
                action: 'startAutomation',
                prompts: this.prompts,
                currentIndex: this.currentPromptIndex,
                waitTime: this.waitTime * 1000 // Convert to milliseconds
            });
        } catch (error) {
            console.error('Error starting automation:', error);
            this.status.textContent = `Error: ${error.message}`;
            this.isRunning = false;
            this.startBtn.disabled = false;
            this.stopBtn.disabled = true;
            this.status.classList.remove('updating');
        }
    }
    
    async stopAutomation() {
        this.isRunning = false;
        this.startBtn.disabled = false;
        this.stopBtn.disabled = true;
        this.status.textContent = 'Automation stopped';
        this.status.classList.remove('updating');

        try {
            // Firefox compatibility: use browser.tabs if chrome.tabs fails
            const tabsAPI = typeof browser !== 'undefined' ? browser.tabs : chrome.tabs;
            const tabs = await tabsAPI.query({ active: true, currentWindow: true });
            if (tabs && tabs[0]) {
                await tabsAPI.sendMessage(tabs[0].id, { action: 'stopAutomation' });
            }
        } catch (error) {
            console.error('Error stopping automation:', error);
        }
    }
    
    handleMessage(message, sender, sendResponse) {
        switch (message.action) {
            case 'updateStatus':
                this.status.textContent = message.status;
                break;
                
            case 'updateProgress':
                this.currentPromptIndex = message.currentIndex;
                this.updatePromptDisplay();
                this.saveData();
                break;
                
            case 'automationComplete':
                this.isRunning = false;
                this.startBtn.disabled = false;
                this.stopBtn.disabled = true;
                this.status.textContent = 'All prompts processed!';
                this.status.classList.remove('updating');
                this.currentPromptIndex = 0;
                this.updatePromptDisplay();
                this.saveData();
                break;
                
            case 'automationError':
                this.isRunning = false;
                this.startBtn.disabled = false;
                this.stopBtn.disabled = true;
                this.status.textContent = `Error: ${message.error}`;
                this.status.classList.remove('updating');
                break;
        }
        
        sendResponse({ received: true });
    }
    
    openInWindow() {
        const width = 400;
        const height = 600;
        const left = (screen.width - width) / 2;
        const top = (screen.height - height) / 2;

        // Firefox compatibility
        const windowsAPI = typeof browser !== 'undefined' ? browser.windows : chrome.windows;
        const runtimeAPI = typeof browser !== 'undefined' ? browser.runtime : chrome.runtime;

        windowsAPI.create({
            url: runtimeAPI.getURL('popup.html'),
            type: 'popup',
            width: width,
            height: height,
            left: Math.round(left),
            top: Math.round(top)
        });

        // Close the current popup
        window.close();
    }
}

// Initialize the automator when the popup loads
document.addEventListener('DOMContentLoaded', () => {
    new AIImageAutomator();
});
