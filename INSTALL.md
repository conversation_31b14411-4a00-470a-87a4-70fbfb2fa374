# Installation Guide - AI Image Automator Firefox Extension

## Quick Start

### Option 1: Automatic Packaging (Recommended)
```bash
./package.sh
```
This creates an `ai-image-automator.xpi` file that you can install directly in Firefox.

### Option 2: Manual Installation (Development)

1. **Open Firefox Developer Tools**
   - Navigate to `about:debugging` in Firefox
   - Click "This Firefox" in the left sidebar

2. **Load the Extension**
   - Click "Load Temporary Add-on"
   - Navigate to this directory and select `manifest.json`
   - The extension will appear in your toolbar

## Testing the Extension

1. **Open the test page**
   - Open `test_page.html` in Firefox
   - This simulates an AI image generation website

2. **Load sample prompts**
   - Click the extension icon in your toolbar
   - Click "Select Text File with Prompts"
   - Choose `sample_prompts.txt`

3. **Start automation**
   - Set wait time (e.g., 5 seconds for testing)
   - Click "Start Automation"
   - Watch the extension automatically fill prompts and click generate!

## Using with Real AI Websites

The extension works with most AI image generation websites including:
- Midjourney
- DALL-E
- Stability AI
- Leonardo AI
- Runware AI
- And many more!

### Steps:
1. Navigate to your AI image generation website
2. Make sure you're logged in and on the generation page
3. Click the extension icon
4. Load your prompts file
5. Set appropriate wait time (30-60 seconds recommended)
6. Click "Start Automation"

## Troubleshooting

### Extension not appearing
- Refresh Firefox after installation
- Check `about:addons` to see if it's listed
- Try reloading the extension in `about:debugging`

### Automation not working
- Make sure the website has a visible prompt textarea
- Check that the generate button is clickable
- Try the test page first to verify extension functionality
- Some websites may have anti-automation measures

### Generate button not found
- The extension looks for common button patterns
- Make sure you're on the actual generation page
- Some sites load content dynamically - wait for full page load

## File Formats

### Prompts File
- Use plain text files (.txt)
- One prompt per line
- Empty lines are ignored
- UTF-8 encoding supported

Example:
```
A beautiful sunset over mountains
A futuristic city with flying cars
A peaceful forest scene
```

## Features

- ✅ Automatic prompt processing
- ✅ Customizable wait times
- ✅ Progress tracking
- ✅ Popup to window conversion
- ✅ Smart element detection
- ✅ Error handling and recovery
- ✅ State persistence

## Security & Privacy

- Extension only accesses the current tab when active
- No data sent to external servers
- All processing happens locally
- Prompts stored locally in browser storage

## Support

If you encounter issues:
1. Test with the included `test_page.html`
2. Check browser console for error messages
3. Try different AI generation websites
4. Ensure the website elements are visible and clickable

Happy automating! 🤖✨
