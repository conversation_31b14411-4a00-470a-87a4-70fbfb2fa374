* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
    min-height: 100vh;
}

.container {
    width: 380px;
    min-height: 500px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    overflow: hidden;
}

.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header h1 {
    font-size: 18px;
    font-weight: 600;
}

.window-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 6px;
    padding: 8px;
    color: white;
    cursor: pointer;
    transition: background 0.2s;
}

.window-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

.section {
    padding: 16px 20px;
    border-bottom: 1px solid #f0f0f0;
}

.section:last-child {
    border-bottom: none;
}

.file-label {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 16px;
    background: #f8f9fa;
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s;
    font-weight: 500;
}

.file-label:hover {
    background: #e9ecef;
    border-color: #667eea;
}

.file-name {
    margin-top: 8px;
    font-size: 14px;
    color: #666;
    font-style: italic;
}

label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #555;
}

.input-field {
    width: 100%;
    padding: 10px 12px;
    border: 2px solid #e1e5e9;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.2s;
}

.input-field:focus {
    outline: none;
    border-color: #667eea;
}

.prompt-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
    color: #666;
}

.btn {
    padding: 12px 20px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    gap: 8px;
    margin-right: 10px;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: #dc3545;
    color: white;
}

.btn-secondary:hover:not(:disabled) {
    background: #c82333;
    transform: translateY(-1px);
}

.status {
    font-size: 14px;
    color: #666;
    margin-bottom: 10px;
    font-weight: 500;
}

.progress-bar {
    width: 100%;
    height: 6px;
    background: #e9ecef;
    border-radius: 3px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    width: 0%;
    transition: width 0.3s ease;
}

.help-section {
    margin-top: 10px;
}

.help-section summary {
    cursor: pointer;
    font-weight: 500;
    color: #667eea;
    padding: 8px 0;
}

.help-section ol {
    margin-top: 10px;
    padding-left: 20px;
}

.help-section li {
    margin-bottom: 6px;
    font-size: 14px;
    color: #666;
    line-height: 1.4;
}

/* Animation for status updates */
.status.updating {
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}
