<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Page for AI Automator</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f0f0f0;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        textarea {
            width: 100%;
            height: 100px;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            resize: vertical;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-top: 10px;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        button:hover:not(:disabled) {
            background: #0056b3;
        }
        .status {
            margin-top: 20px;
            padding: 10px;
            background: #e9ecef;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>AI Image Generator Test Page</h1>
        <p>This page simulates the Runware AI interface for testing the extension.</p>
        
        <div>
            <label for="prompt">Enter your prompt:</label>
            <textarea id="prompt" placeholder="Type your prompt here to start..."></textarea>
        </div>
        
        <button id="generateBtn" disabled>Generate</button>
        
        <div class="status" id="status">
            Ready to generate images. Enter a prompt to enable the generate button.
        </div>
    </div>

    <script>
        const textarea = document.getElementById('prompt');
        const button = document.getElementById('generateBtn');
        const status = document.getElementById('status');
        
        // Enable/disable button based on textarea content
        textarea.addEventListener('input', function() {
            if (this.value.trim().length > 0) {
                button.disabled = false;
                status.textContent = 'Ready to generate!';
            } else {
                button.disabled = true;
                status.textContent = 'Ready to generate images. Enter a prompt to enable the generate button.';
            }
        });
        
        // Simulate generation process
        button.addEventListener('click', function() {
            button.disabled = true;
            status.textContent = 'Generating image... Please wait.';
            
            // Simulate processing time
            setTimeout(() => {
                status.textContent = 'Image generated successfully! Enter another prompt to continue.';
                textarea.value = '';
                button.disabled = true;
            }, 2000);
        });
        
        // Log for debugging
        console.log('Test page loaded. Textarea:', textarea, 'Button:', button);
    </script>
</body>
</html>
