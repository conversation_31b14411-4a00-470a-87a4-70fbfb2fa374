// Background script for AI Image Automator Extension

class BackgroundManager {
    constructor() {
        this.initializeExtension();
    }
    
    initializeExtension() {
        // Handle extension installation
        chrome.runtime.onInstalled.addListener((details) => {
            if (details.reason === 'install') {
                console.log('AI Image Automator installed');
                this.showWelcomeNotification();
            } else if (details.reason === 'update') {
                console.log('AI Image Automator updated');
            }
        });
        
        // Handle messages from popup and content scripts
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            this.handleMessage(message, sender, sendResponse);
            return true; // Keep message channel open for async responses
        });
        
        // Handle browser action click (when popup is disabled)
        chrome.browserAction.onClicked.addListener((tab) => {
            this.handleBrowserActionClick(tab);
        });
        
        // Handle tab updates to inject content script if needed
        chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
            if (changeInfo.status === 'complete' && tab.url) {
                this.handleTabUpdate(tabId, tab);
            }
        });
    }
    
    showWelcomeNotification() {
        // Create a welcome notification
        if (chrome.notifications) {
            chrome.notifications.create('welcome', {
                type: 'basic',
                iconUrl: 'icons/icon-48.png',
                title: 'AI Image Automator Installed!',
                message: 'Click the extension icon to start automating your AI image generation.'
            });
        }
    }
    
    handleMessage(message, sender, sendResponse) {
        switch (message.action) {
            case 'openPopupWindow':
                this.openPopupWindow();
                sendResponse({ success: true });
                break;
                
            case 'getTabInfo':
                this.getActiveTabInfo(sendResponse);
                break;
                
            case 'injectContentScript':
                this.injectContentScript(message.tabId, sendResponse);
                break;
                
            default:
                sendResponse({ success: false, error: 'Unknown action' });
        }
    }
    
    handleBrowserActionClick(tab) {
        // This is called when the extension icon is clicked and no popup is shown
        // We can use this as a fallback to open the popup window
        this.openPopupWindow();
    }
    
    handleTabUpdate(tabId, tab) {
        // Check if this is a page where we might want to inject our content script
        // This is useful for single-page applications that change URLs dynamically
        if (this.isRelevantPage(tab.url)) {
            // Content script should already be injected via manifest, but we can
            // send a message to check if it's active
            chrome.tabs.sendMessage(tabId, { action: 'ping' }, (response) => {
                if (chrome.runtime.lastError) {
                    // Content script not responding, might need to inject manually
                    console.log('Content script not active on tab', tabId);
                }
            });
        }
    }
    
    isRelevantPage(url) {
        if (!url) return false;
        
        // List of domains/patterns where AI image generation might happen
        const relevantPatterns = [
            'midjourney.com',
            'openai.com',
            'stability.ai',
            'runware.ai',
            'leonardo.ai',
            'playground.ai',
            'huggingface.co',
            'replicate.com'
        ];
        
        return relevantPatterns.some(pattern => url.includes(pattern));
    }
    
    openPopupWindow() {
        const width = 400;
        const height = 600;
        
        // Get screen dimensions to center the window
        chrome.system.display.getInfo((displays) => {
            const primaryDisplay = displays[0];
            const left = Math.round((primaryDisplay.bounds.width - width) / 2);
            const top = Math.round((primaryDisplay.bounds.height - height) / 2);
            
            chrome.windows.create({
                url: chrome.runtime.getURL('popup.html'),
                type: 'popup',
                width: width,
                height: height,
                left: left,
                top: top,
                focused: true
            });
        });
    }
    
    async getActiveTabInfo(sendResponse) {
        try {
            const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
            if (tabs.length > 0) {
                sendResponse({
                    success: true,
                    tab: {
                        id: tabs[0].id,
                        url: tabs[0].url,
                        title: tabs[0].title
                    }
                });
            } else {
                sendResponse({ success: false, error: 'No active tab found' });
            }
        } catch (error) {
            sendResponse({ success: false, error: error.message });
        }
    }
    
    async injectContentScript(tabId, sendResponse) {
        try {
            await chrome.tabs.executeScript(tabId, { file: 'content.js' });
            sendResponse({ success: true });
        } catch (error) {
            sendResponse({ success: false, error: error.message });
        }
    }
    
    // Utility method to store data
    async storeData(key, data) {
        return new Promise((resolve, reject) => {
            chrome.storage.local.set({ [key]: data }, () => {
                if (chrome.runtime.lastError) {
                    reject(chrome.runtime.lastError);
                } else {
                    resolve();
                }
            });
        });
    }
    
    // Utility method to retrieve data
    async getData(key) {
        return new Promise((resolve, reject) => {
            chrome.storage.local.get([key], (result) => {
                if (chrome.runtime.lastError) {
                    reject(chrome.runtime.lastError);
                } else {
                    resolve(result[key]);
                }
            });
        });
    }
}

// Initialize the background manager
const backgroundManager = new BackgroundManager();

// Handle cleanup when extension is disabled/uninstalled
chrome.runtime.onSuspend.addListener(() => {
    console.log('AI Image Automator suspended');
});

// Error handling for uncaught errors
self.addEventListener('error', (event) => {
    console.error('Background script error:', event.error);
});

self.addEventListener('unhandledrejection', (event) => {
    console.error('Unhandled promise rejection in background script:', event.reason);
});
